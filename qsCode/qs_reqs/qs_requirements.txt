#everytime a new requirement comes copy that as qs_requirements.txt comment torch line so that qs environment is created fresh
#torch=2.5.1+cu124
#torchaudio=2.5.1+cu124
#torchvision=0.20.1+cu124
torchsde
comfyui-frontend-package==1.10.17
numpy>=1.25.0
einops
transformers>=4.28.1
tokenizers>=0.13.3
sentencepiece
safetensors>=0.4.2
aiohttp>=3.11.8
yarl>=1.18.0
pyyaml
Pillow
scipy
tqdm
psutil
scikit-image
#safetensors

#non essential dependencies:
kornia>=0.7.1
spandrel
soundfile
av
ffmpeg-python==0.2.0
