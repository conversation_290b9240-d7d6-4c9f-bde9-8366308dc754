#!/bin/bash
echo "comfy start at boot after safe mounting custom_nodes"
base_dir=$(dirname "$0")
echo "running in $base_dir"

$base_dir/qs_safe_mount.sh $base_dir/qs_custom_nodes $base_dir/custom_nodes ALL

cd $base_dir
./mount_other_dirs.sh
./mount_custom_nodes.sh
./qs_mount.sh
#now start comfy
echo "Starting 1: comfy client mode  from $base_dir  Logs will come to  /tmp/comfy_boot_28890.out base port 28890 nginx port 18890" 
eval "$(conda shell.bash hook)" && conda deactivate  && conda activate qs
nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
      --extra-model-paths-config config/priority_models.yaml  --use-pytorch-cross-attention --port 28890 --run-mode client 2>&1 >/tmp/comfy_28890.out &
disown

echo "comfy started 2 processes"

