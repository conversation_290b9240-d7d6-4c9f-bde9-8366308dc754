#!/bin/bash
# Arguments: $1 = source_dir, $2 = nodes_base_dir, $3 = target_dir
SOURCE_DIR=$1
TARGET_DIR=$2

while IFS= read -r line; do
    SRC_PATH="$SOURCE_DIR/$line"
    TARGET_PATH="$TARGET_DIR/$line"
    if [ -e "$SRC_PATH" ]; then
        mkdir -p "$(dirname "$TARGET_PATH")"
        cp -r "$SRC_PATH" "$TARGET_PATH"
    else
        echo "Warning: $line does not exist in the source directory."
    fi
done < /ComfyUI/fixed_nodes.txt
