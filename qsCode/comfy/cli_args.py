
import enum
from comfy.cli_args import EnumAction
class RunMode(enum.Enum):
    DEVELOPMENT = "development"
    API = "api"
    CLIENT = "client"

def qs_add_args(parser,args):
    if args.run_mode == RunMode.API:
        # In API mode, disable UI rendering
        args.disable_ui = True
        # Set a flag to indicate API mode for server routing
        args.api_only = True
    print('running in run mode: ', args.run_mode)
