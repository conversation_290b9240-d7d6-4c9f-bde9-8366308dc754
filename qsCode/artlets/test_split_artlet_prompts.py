import json
from split_artlet_prompts import split_artlet_prompts, save_prompt

def test_with_example():
    """Test the split_artlet_prompts function with the example JSON file."""
    # Load the example JSON
    with open('../artlet/sample_workflow.json', 'r') as f:
        prompt = json.load(f)
    
    # Define server nodes (nodes 4, 5, 6 as mentioned in the requirements)
    server_node_list = ['3', '5', '6', '7', '8']
    
    # Split the prompt
    client_prompt, server_prompt = split_artlet_prompts(prompt, server_node_list)
    
    # Save the results for inspection
    with open('../artlet/client_prompt_test.json', 'w') as f:
        json.dump(client_prompt, f, indent=4)
    
    with open('../artlet/server_prompt_test.json', 'w') as f:
        json.dump(server_prompt, f, indent=4)
    
    # Print some information about the split
    print(f"Original prompt has {len(prompt)} nodes")
    print(f"Client prompt has {len(client_prompt)} nodes")
    print(f"Server prompt has {len(server_prompt)} nodes")
    
    # Verify that server nodes are in server prompt and not in client prompt
    for node_id in server_node_list:
        assert node_id in server_prompt, f"Server node {node_id} should be in server prompt"
        assert node_id not in client_prompt, f"Server node {node_id} should not be in client prompt"
    
    # Verify that client nodes are in client prompt
    for node_id in prompt:
        if node_id not in server_node_list:
            assert node_id in client_prompt, f"Client node {node_id} should be in client prompt"
    
    # Verify that new blocks were added
    assert any(node_data.get('class_type') == 'ArtletClientBlock' for node_data in client_prompt.values()), \
        "Client prompt should contain an ArtletClientBlock"
    
    assert any(node_data.get('class_type') == 'ArtletServerInputBlock' for node_data in server_prompt.values()), \
        "Server prompt should contain an ArtletServerInputBlock"
    
    assert any(node_data.get('class_type') == 'ArtletServerOutputBlock' for node_data in server_prompt.values()), \
        "Server prompt should contain an ArtletServerOutputBlock"
    
    print("All tests passed!")

if __name__ == "__main__":
    test_with_example()
