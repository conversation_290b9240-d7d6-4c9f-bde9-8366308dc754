#This file builds docker image. 
#Tags used so that it doesnt affect existing

#qsComfy:prod_latest
#qsComfy:dev_latest
#qsComfy:latest (on the machine)
#qsComfy:comfy_ver.n/s/h_0.1
#qsComfy:comfy_ver.dt   #frozen version

#docker build tag #force not to use latest
#docker tag source_tag dest_tag

#!/bin/bash

acrAppId="f29266b4-f797-424d-897c-fd4644b1e518"
acrPwd="****************************************"
acrTenant="73b55c4d-0679-4a00-acbf-3ef8759d1c31"

ACR_NAME="qsacr001"
echo "usage docker_tag.sh source_tag dest_tag"
if [ "$#" -lt 2 ]; then
    echo "Missing arguments usage docker_tag.sh source_tag dest_tag push"
    exit 1
fi

SOURCE_TAG=$1
TARGET_TAG=$2
az login --service-principal --username $acrAppId --password $acrPwd --tenant $acrTenant && az acr login --name qsacr001 
if [ $? -ne 0 ]; then
    echo "Failed to login to ACR"
    exit 1
fi
docker tag $ACR_NAME.azurecr.io/qs-comfy:$SOURCE_TAG $ACR_NAME.azurecr.io/qs-comfy:$TARGET_TAG

docker push $ACR_NAME.azurecr.io/$TARGET_TAG
