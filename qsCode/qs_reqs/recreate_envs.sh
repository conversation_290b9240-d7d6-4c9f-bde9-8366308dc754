#!/bin/bash
# This scrit is to test if check if newer version frozen_reqs.txt is working
# 1st parameter is given take it as requirements file 2nd parameter is given take it as env_name

frozen_reqs="${1:-/qsfs2/services/qsComfy/qsCode/qs_reqs/frozen_requirements.txt}"
env_name="${2:-qs}"
base_env_name="${env_name}base"
echo running with $frozen_reqs and $env_name and $base_env_name
source ~/.bashrc
#check if frozen_reqs file exists
if [ ! -f "$frozen_reqs" ]; then
    echo "$frozen_reqs file not found"
    exit 1
fi
# check if test_env directory exists
if [ -d "/home/<USER>/.conda/envs/$env_name" ] || [ -d "/home/<USER>/.conda/envs/${base_env_name}" ]; then
    echo "$env_name environment exist, do you want to delete and continue? (yes/no)"
    conda rename -n qs   qs.bak
    conda rename -n qsbase qsbase.bak
fi

# delete current existing envs
conda env remove -n $env_name -y
conda env remove -n ${base_env_name} -y

#deactivate any current envs
eval "$(conda shell.bash hook)" && conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null 


conda create --name ${base_env_name} python=3.12.2 -y && eval "$(conda shell.bash hook)" && unset PYTHONPATH && conda activate ${base_env_name}
conda install conda-forge::ffmpeg

pip install torch==2.5.1+cu124 torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124
pip install torch==2.5.1+cu124 -U  xformers --index-url https://download.pytorch.org/whl/cu124

pip install -r ${frozen_reqs} 

# check if torch is working 
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"
if [ $? -eq 0 ]; then 
    echo "torch working with new requirements"
else
    echo "torch not working with new requirements, do you want to delete test envs? (yes/no)"
fi
echo "base envrionment ${base_env_name} env setup is complete"

conda deactivate

#now create conda env ${env_name} and update pythonpath,
conda create --name $env_name python=3.12.2 -y

mkdir -p /home/<USER>/.conda/envs/$env_name/etc/conda/activate.d
mkdir -p /home/<USER>/.conda/envs/$env_name/etc/conda/deactivate.d

cp /qsfs2/services/qsComfy/qsCode/qs_reqs/env_vars.sh /tmp/
cp /qsfs2/services/qsComfy/qsCode/qs_reqs/unset_env_vars.sh /tmp/
sed -i "s|qsbase|$base_env_name|g" /tmp/env_vars.sh
sed -i "s|qsbase|$base_env_name|g" /tmp/unset_env_vars.sh

cp /tmp/env_vars.sh /home/<USER>/.conda/envs/$env_name/etc/conda/activate.d/
chmod +x /home/<USER>/.conda/envs/$env_name/etc/conda/activate.d/env_vars.sh
cp /tmp/unset_env_vars.sh /home/<USER>/.conda/envs/$env_name/etc/conda/deactivate.d/
chmod +x /home/<USER>/.conda/envs/$env_name/etc/conda/deactivate.d/unset_env_vars.sh

unset PYTHONPATH && conda activate ${env_name}
#check if torch is working
python -c "import torch; print(torch.__version__); print(torch.cuda.is_available())"
if [ $? -eq 0 ]; then 
    echo "torch working with new requirements"
else
    echo "torch not working with new requirements, do you want to delete test envs? (yes/no)"
fi
conda deactivate ; conda deactivate ; conda deactivate
echo "new env ${base_env_name} and ${env_name} created, run comfyui and check"
