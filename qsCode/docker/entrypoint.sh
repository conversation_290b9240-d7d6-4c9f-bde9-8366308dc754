#!/bin/bash

eval "$(conda shell.bash hook)" && conda deactivate && conda activate qs
    conda env list
run_mode="development"  #we are running all in development for now
for port in 28890 ; do
  # case $port in
  #   28890) run_mode="client" ;;
  #   28891) run_mode="api" ;;
  #   28892) run_mode="development" ;;
  # esac
  log_file="/tmp/log_${port}.txt"
  error_file="/tmp/error_${port}.txt"
  python main.py --extra-model-paths-config config/docker_priority_models.yaml --port $port --run-mode $run_mode ${QSCOMFY_ARGS} > ${log_file} 2> ${log_file} &
done

# Prevent the entrypoint.sh script from exiting
tail -f /dev/null