import os
import importlib

def loadQsExtraNodes(NODE_CLASS_MAPPINGS,NODE_DISPLAY_NAME_MAPPINGS , EXTENSION_WEB_DIRS):
    #import all the py files under qs_extra  go through all of them and load the nodes
    # get the dicts NODE_CLASS_MAPPINGS and NODE_DISPLAY_NAME_MAPPINGS from each file and append them to the global NODE_CLASS_MAPPINGS and NODE_DISPLAY_NAME_MAPPINGS
    # if the file does not have these dicts, skip it
    try:
        qs_extra_path = os.path.join(os.path.dirname(__file__),"../../", "qs_extras")
        for file in os.listdir(qs_extra_path):
            if file.endswith(".py"):
                try:
                    module_path = os.path.join(qs_extra_path, file)
                    sp = os.path.splitext(module_path)
                    module_name = sp[0]
        
                    spec = importlib.util.spec_from_file_location(module_name, module_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    if hasattr(module, "NODE_CLASS_MAPPINGS") and hasattr(module, "NODE_DISPLAY_NAME_MAPPINGS"):
                        NODE_CLASS_MAPPINGS.update(module.NODE_CLASS_MAPPINGS)
                        NODE_DISPLAY_NAME_MAPPINGS.update(module.NODE_DISPLAY_NAME_MAPPINGS)
                        if hasattr(module, "WEB_DIRECTORY") and getattr(module, "WEB_DIRECTORY") is not None:
                            web_dir = os.path.abspath(os.path.join(qs_extra_path, getattr(module, "WEB_DIRECTORY")))
                            if os.path.isdir(web_dir):
                                EXTENSION_WEB_DIRS[module_name] = web_dir
                                print(f'found WEB_DIRECTORY for {module_name}: {web_dir} added')           
                except Exception as e:
                    print(f"Failed to load qs_extra node file {file}: {e}")        
    except Exception as e:
        print(f"Failed to load qs_extra nodes: {e}")

