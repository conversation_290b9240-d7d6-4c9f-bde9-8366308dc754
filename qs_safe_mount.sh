#!/bin/bash
# Default values
echo "this mounts in safe moude if something already exists will not try to remount "
#make script dir as default source dir
base_dir=$(dirname "$0")
DEFAULT_SOURCE_DIR=$base_dir/qs_custom_nodes/
DEFAULT_TARGET_DIR=$base_dir/custom_nodes

# Read arguments
SOURCE_DIR="${1:-$DEFAULT_SOURCE_DIR}"
TARGET_DIR="${2:-$DEFAULT_TARGET_DIR}"
NODE_DIR="${3:-ALL}"

echo " This safe unmounts if something already exists will not try to remount if you need to force use qs_mount.sh"
 
echo "Usage: $0 [source_dir] [target_dir] [node_dir]"
# Step 2: Bindfs mount node directories
if [ "$NODE_DIR" == "ALL" ]; then
    echo "Mounting all node directories from $SOURCE_DIR to $TARGET_DIR..."
    for dir in "$SOURCE_DIR"/*; do
        if [ -d "$dir" ]; then  # Check if it is a directory
            node_name=$(basename "$dir")
            target_node_dir="$TARGET_DIR/$node_name"
            # Ensure the target directory exists
            mkdir -p "$target_node_dir"
            #check if the target directory is  mounted already if so unmount it
            grep "fuse" /proc/mounts | awk -v target="$target_node_dir" '$2 ~ "^"target {print $2}' | while read -r mount_point; do
                echo "Found existing mout point $mount_point "
                #continue do not mount
                continue
            done
            # Mount using bindfs
            echo "Mounting $dir to $target_node_dir...only if it is empty "
            sudo bindfs --perms=a+w "$dir" "$target_node_dir" 2> /dev/null
        else
            echo "Skipping non-directory: $dir"
        fi
    done
    #all mounts are done exit with 0 status for further processing"
    exit 0
else
    echo "Mounting node directory $NODE_DIR from $SOURCE_DIR to $TARGET_DIR..."
    source_node_dir="$SOURCE_DIR/$NODE_DIR"
    target_node_dir="$TARGET_DIR/$NODE_DIR"
    
    # Check if the source is a directory
    if [ -d "$source_node_dir" ]; then
        # Ensure the target directory exists
        mkdir -p "$target_node_dir"
        #check if the target directory is  mounted already if so unmount it
        grep "fuse" /proc/mounts | awk -v target="$target_node_dir" '$2 ~ "^"target {print $2}' | while read -r mount_point; do
	echo "found existing mount $target_dir"
        done
            
        # Mount using bindfs
        echo "Mounting $source_node_dir to $target_node_dir...only if non empty"
        sudo bindfs --perms=a+w "$source_node_dir" "$target_node_dir"
    
       exit 0
    else
        echo "Error: Source directory $source_node_dir does not exist or is not a directory!"
        exit 1
    fi
fi

echo "Mount(s) completed :-)"

