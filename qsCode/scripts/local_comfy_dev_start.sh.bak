#!/bin/bash
echo "comfy start at boot after safe mounting custom_nodes"
base_dir=$(dirname "$0")
echo "running in $base_dir"
#this can not run in services directory. check if the directory contains services if so exit
if [[ $base_dir == *"services"* ]]; then
    echo "This script can not run in services directory. Run it from qsusers directory"
    exit 1
fi

cd $base_dir
./mount_other_dirs.sh
./mount_custom_nodes.sh
./qs_mount.sh

#now start comfy
eval "$(conda shell.bash hook)" && conda deactivate  && conda activate qs
echo "starting developer comfy server from $base_dir  Logs will come to /tmp/comfy_boot_28893.out base port 28893 nginx port 18893" 
nohup python main.py --enable-cors-header --multi-user --listen 0.0.0.0 \
               --use-pytorch-cross-attention --port 28893 2>&1 >/tmp/comfy_28893.out &
disown

echo "comfy started  processes"

