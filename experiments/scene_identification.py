import cv2
import os
import numpy as np

def calculate_histogram_diff(frame1, frame2):
    # Convert to HSV for better color sensitivity
    hsv1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2HSV)
    hsv2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2HSV)

    # Calculate histogram and normalize
    hist1 = cv2.calcHist([hsv1], [0, 1], None, [50, 60], [0, 180, 0, 256])
    hist2 = cv2.calcHist([hsv2], [0, 1], None, [50, 60], [0, 180, 0, 256])
    cv2.normalize(hist1, hist1)
    cv2.normalize(hist2, hist2)

    # Use correlation method to compare
    diff = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
    return 1 - diff  # Higher difference means more likely a cut

def detect_scene_changes(video_path, output_dir, threshold=0.5, min_scene_gap=2):
    os.makedirs(output_dir, exist_ok=True)
    cap = cv2.VideoCapture(video_path)
    
    prev_frame = None
    frame_id = 0
    last_saved_frame = - min_scene_gap
    scene_change_count = 0

    print("Detecting scene changes...")
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        if prev_frame is not None:
            diff = calculate_histogram_diff(prev_frame, frame)
            if diff > threshold and (frame_id - last_saved_frame) > min_scene_gap:
                print(f"Scene change at frame {frame_id} (diff={diff:.2f})")
                cv2.imwrite(os.path.join(output_dir, f"scene_{frame_id:05d}.png"), frame)
                last_saved_frame = frame_id
                scene_change_count += 1
        else : # save the first frame as starting point
            cv2.imwrite(os.path.join(output_dir, f"scene_{frame_id:05d}.png"), frame)          
        prev_frame = frame
        frame_id += 1

    cap.release()
    print("Done.")
    print(f"Total scene changes: {scene_change_count}")
    return scene_change_count

# for f in os.listdir("/mnt/c/Users/<USER>/Downloads/scene_change_test"):
#     if f.endswith(".mp4") or f.endswith(".mov"):
#         print(f"Processing {f}")
#         scene_change_count = detect_scene_changes(f"/mnt/c/Users/<USER>/Downloads/scene_change_test/{f}",
#                                                    f"/mnt/c/Users/<USER>/Downloads/scene_change_test/scene_changes_{f}",threshold=0.3,min_scene_gap=2)
#         print(f"Total scene changes: {scene_change_count}")
# Example usage:
video_path = "/mnt/c/Users/<USER>/Downloads/aadukalam_sc2_480p.mp4"
output_dir = "/mnt/c/Users/<USER>/Downloads/scene_changes"
detect_scene_changes(video_path, output_dir, threshold=0.4)
