import torch
import numpy as np

class QSMaskPaddingNode:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "mask": ("MASK", ),
                "left": ("INT", {"default": 10, "min": 0, "step":5}),
                "top": ("INT", {"default": 10, "min": 0,"step":5}),
                "right": ("INT", {"default": 10, "min": 0,"step":5}),
                "bottom": ("INT", {"default": 10, "min": 0,"step":5}),
            }
        }

    RETURN_TYPES = ("MASK",)
    FUNCTION = "apply_padding"
    CATEGORY = "QS/mask"

    def apply_padding(self, mask, left, top, right, bottom):
        device = mask.device  # Maintain same device
        batch, h, w = mask.shape  # Get batch size, height, and width
        padded_masks = torch.zeros_like(mask, device=device)  # Initialize output masks

        for i in range(batch):
            single_mask = mask[i]  # Process each mask individually
            coords = torch.nonzero(single_mask)

            if coords.numel() == 0:
                padded_masks[i] = single_mask  # If no white region, keep original
                continue

            y_min, x_min = coords[:, 0].min(), coords[:, 1].min()
            y_max, x_max = coords[:, 0].max(), coords[:, 1].max()

            # Apply padding with boundary constraints
            y_min = max(y_min - top, 0)
            x_min = max(x_min - left, 0)
            y_max = min(y_max + bottom, h - 1)
            x_max = min(x_max + right, w - 1)

            # Assign padded region to output mask
            padded_masks[i, y_min:y_max + 1, x_min:x_max + 1] = 1

        return (padded_masks,)


class QsSelectNframes:
    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {
                "video": ("IMAGE",),                       # (b,3,h,w)
                "n": ("INT", {"default": 81, "min": 1}),
                "conditional_frame_index": ("INT", {"default": 0, "min": 0}),
                "change_mask": ("BOOLEAN", {"default": True}),
            },
            "optional": {
                "conditional_frame": ("IMAGE",),          # (1,3,h,w)
                "video_mask": ("IMAGE",),                 # (b,3,h,w)
            },
        }

    RETURN_TYPES = ("IMAGE", "LIST", "IMAGE", "INT", "INT")
    RETURN_NAMES = (
        "n_frames", "frame_indices",
        "n_mask_frames", "append_num",
        "conditional_frame_new_index",
    )
    FUNCTION = "select_frames"
    CATEGORY = "QS/video"

    # ──────────────────────────────────────────────────────────
    def select_frames(
        self, video, n, conditional_frame_index = 0, change_mask = True, conditional_frame= None, video_mask = None):
        device = video.device
        b = video.shape[0]                               # total frames

        if b < n:
            idx_t = torch.arange(b, device=device)
            n_frames = video.clone()
            
            # Extend by repeating last frame
            last_frame = video[-1].unsqueeze(0)
            extra = last_frame.repeat(n - b, 1, 1, 1)
            n_frames = torch.cat([n_frames, extra], dim=0)

            # Frame indices
            indices = list(range(b)) + [b - 1] * (n - b)
            append_num = n - b
            conditional_frame_new_index = min(conditional_frame_index, b - 1)

            # Mask processing
            if video_mask is not None:
                n_mask_frames = video_mask.index_select(0, idx_t)
                extra_mask = video_mask[-1].unsqueeze(0).repeat(n - b, 1, 1, 1)
                n_mask_frames = torch.cat([n_mask_frames, extra_mask], dim=0)
            else:
                n_mask_frames = torch.zeros_like(n_frames)

            if change_mask:
                n_mask_frames[conditional_frame_new_index] = torch.zeros_like(n_mask_frames[0])

            return n_frames, indices, n_mask_frames, append_num, conditional_frame_new_index


        # Clamp conditional idx inside video
        c_idx = int(max(0, min(b - 1, conditional_frame_index)))

        # Replace original frame if a custom one is provided
        if conditional_frame is not None:
            if conditional_frame.ndim == 4 and conditional_frame.shape[0] == 1:
                video[c_idx] = conditional_frame[0]
            else:
                raise ValueError("conditional_frame must be shape (1,C,H,W)")

        # Base even spread
        if b >= n:
            base = np.linspace(0, b - 1, n).round().astype(int).tolist()
            indices = sorted(set(base))                  # remove dups
        else:
            indices = list(range(b))                     # shorter clip

        # Guarantee conditional frame presence
        if c_idx not in indices:
            indices.append(c_idx)
            indices.sort()

        # Fill missing slots with unused indices
        while len(indices) < n:
            for cand in range(b):
                if cand not in indices:
                    indices.append(cand)
                    if len(indices) == n:
                        break

        append_num = 0
        if len(indices) < n:                             # duplicate last
            append_num = n - len(indices)
            indices.extend([b - 1] * append_num)

        # ── Protected trim: never drop conditional idx ──
        while len(indices) > n:
            gaps = np.diff(indices)
            k = int(np.argmin(gaps))                     # second of tightest pair
            to_remove = indices[k + 1]                   # candidate
            if to_remove == c_idx:                       # protect c_idx
                to_remove = indices[k]                   # drop other member
                if to_remove == c_idx:                   # both are c_idx
                    # fallback: first non‑conditional frame
                    for val in indices:
                        if val != c_idx:
                            to_remove = val; break
            indices.remove(to_remove)

        idx_t = torch.tensor(indices, device=device, dtype=torch.long)
        n_frames = video.index_select(0, idx_t)          # (n,3,H,W)

        # Masks
        n_mask_frames = (
            video_mask.index_select(0, idx_t)
            if video_mask is not None
            else torch.zeros_like(n_frames)
        )

        c_new = indices.index(c_idx)                     # locate in n_frames
        if change_mask:
            n_mask_frames[c_new] = torch.zeros_like(n_mask_frames[c_new])

        return n_frames, indices, n_mask_frames, append_num, c_new


class QsJoinFrames:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "original_video": ("IMAGE",),          # (b,3,H,W)
                "replacement_frames": ("IMAGE",),      # (n,3,H,W)
                "frame_indices": ("LIST",),            # list[int] length n
            }
        }

    RETURN_TYPES = ("IMAGE",)
    RETURN_NAMES = ("video_out",)
    FUNCTION = "replace_frames"
    CATEGORY = "QS/video"

    # --------------------------------------------------
    def replace_frames(self, original_video, replacement_frames, frame_indices):
        b = original_video.shape[0]
        n = replacement_frames.shape[0]

        # --- Basic validation ---------------------------------
        if n != len(frame_indices):
            raise ValueError("Length of frame_indices must match replacement_frames")
        if replacement_frames.shape[1:] != original_video.shape[1:]:
            raise ValueError("Channel/size mismatch between videos")
        if any(idx < 0 or idx >= b for idx in frame_indices):
            raise IndexError("frame_indices out of range")

        # --- Clone and replace --------------------------------
        video_out = original_video.clone()
        for i, idx in enumerate(frame_indices):
            video_out[idx] = replacement_frames[i]

        return (video_out,)


class QsNSequence:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "original_video": ("IMAGE",),          # (b,3,H,W)
                "n_frames": ("IMAGE",),                # (n,3,H,W)
                "N": ("INT", {"min": 1, "default": 81}),
                "frame_indices": ("LIST",),            # list[int] len = n
                "change_mask": ("BOOLEAN", {"default": True}),
                "append_num": ("INT", {"default": 0}),
            },
            "optional": {
                "video_mask": ("IMAGE",),             # (b,3,H,W)
            },
        }

    RETURN_TYPES = ("IMAGE", "IMAGE", "LIST", "INT")
    RETURN_NAMES = (
        "list_video",
        "list_masked_video",
        "list_append_num",
        "num_batches",
    )
    OUTPUT_IS_LIST = (True, True, False, False)
    FUNCTION = "make_batches"
    CATEGORY = "QS/video"

    # ------------------------------------------------------------------
    def make_batches(self, original_video, n_frames, N, frame_indices, change_mask = True, append_num = 0, video_mask = None):
        
        b = original_video.shape[0]
        device = original_video.device

        # if original_video.shape[1] > 3:
        #     original_video = original_video[:, :3, :, :]

        # --- 1) Trim off appended duplicates --------------------------
        if append_num > 0:
            n_frames = n_frames[:-append_num]
            frame_indices = frame_indices[:-append_num]

        # --- 2) Inject n_frames back into original video --------------
        video_mod = original_video.clone()
        for i, idx in enumerate(frame_indices):
            video_mod[idx] = n_frames[i]

        # --- 3) Prepare mask video ------------------------------------
        if video_mask is not None:
            mask_mod = video_mask.clone()
        else:
            mask_mod = torch.zeros_like(video_mod)

        if change_mask:
            for idx in frame_indices:
                mask_mod[idx] = torch.zeros_like(mask_mod[idx])

        # --- 4) Build overlapping N‑frame batches ---------------------
        list_video = []
        list_mask  = []
        list_append = []

        start = 0
        while start < b:
            end = min(start + N, b)
            batch_v = [video_mod[i] for i in range(start, end)]
            batch_m = [mask_mod[i]  for i in range(start, end)]

            # Need exactly N frames in each batch
            repeated = 0
            if len(batch_v) == N:
                # Check if last frame is in frame_indices (except for final batch)
                if end != b and (end - 1) not in frame_indices:
                    # Walk backward to last frame in frame_indices
                    anchor = None
                    for j in range(end - 1, start - 1, -1):
                        if j in frame_indices:
                            anchor = j
                            break
                    if anchor is not None:
                        # Slice up to anchor and repeat that frame
                        keep_len = anchor - start + 1
                        batch_v = batch_v[:keep_len]
                        batch_m = batch_m[:keep_len]
                        repeated = N - keep_len
                        batch_v.extend([video_mod[anchor]] * repeated)
                        batch_m.extend([mask_mod[anchor]]  * repeated)
            else:
                # Final partial batch – repeat last frame until N
                repeated = N - len(batch_v)
                batch_v.extend([batch_v[-1]] * repeated)
                batch_m.extend([batch_m[-1]] * repeated)

            # Stack to tensors (N,3,H,W)
            list_video.append(torch.stack(batch_v, dim=0).to(device))
            list_mask.append(torch.stack(batch_m,  dim=0).to(device))
            list_append.append(repeated)

            # Overlap: next start is last frame of current batch
            # Determine the true "last frame used" in this batch
            if len(batch_v) == N and end != b and (end - 1) not in frame_indices:
                # Already computed anchor above
                next_start = anchor
            else:
                next_start = start + N - 1

            start = next_start

        num_batches = len(list_video)
        return list_video, list_mask, list_append, num_batches



class QsExtractFromList:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "list_video": ("IMAGE",),               # List of videos
                "index": ("INT", {"default": 0}),       # 0-based index
            },
            "optional": {
                "list_masked_video": ("IMAGE",),        # List of masks (optional)
                "list_append_num": ("LIST",),           # List of append counts (optional)
            }
        }

    RETURN_TYPES = ("IMAGE", "IMAGE", "INT")
    RETURN_NAMES = ("video", "masked_video", "append_num")
    FUNCTION = "extract"
    CATEGORY = "QsCustom"

    # ----------------------------------------------------------------------
    def extract(self, list_video, index, list_masked_video=None, list_append_num=None):
        # --- Validate index ---------------------------------------------
        if index < 0 or index >= len(list_video):
            raise IndexError(f"Index {index} out of range for list_video of length {len(list_video)}")

        # --- Extract the batch video -----------------------------------
        video = list_video[index]

        # --- Extract or fallback mask ----------------------------------
        if list_masked_video is not None:
            masked_video = list_masked_video[index]
        else:
            masked_video = torch.zeros_like(video)

        # --- Extract or fallback append count --------------------------
        if list_append_num is not None and index < len(list_append_num):
            append_num = list_append_num[index]
        else:
            append_num = 0

        return (video, masked_video, append_num)


class QsVideoListCombine:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "list_video": ("IMAGE",),         # List of (N,3,H,W) tensors
                "list_append_num": ("LIST",),     # List of ints
            }
        }

    RETURN_TYPES = ("IMAGE",)  # Final combined video
    RETURN_NAMES = ("video",)
    FUNCTION = "combine"
    CATEGORY = "QS/video"

    def combine(self, list_video, list_append_num):
        assert len(list_video) == len(list_append_num), "Mismatch in lengths of video and append list"
        
        cleaned = []

        for i, (vid, extra) in enumerate(zip(list_video, list_append_num)):
            # Remove appended frames at end
            if extra > 0:
                vid = vid[:-extra]
            
            # Remove first frame of all batches except the first
            if i > 0:
                vid = vid[1:]

            cleaned.append(vid)

        # Concatenate all cleaned chunks into one full video
        final_video = torch.cat(cleaned, dim=0)

        return (final_video,)


class QsCollectToList:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "videos": ("IMAGE",),  # Each input will be one (N,3,H,W) batch
            }
        }

    INPUT_IS_LIST = True  # ComfyUI will pass a list of individual inputs
    RETURN_TYPES = ("IMAGE",)  # We're returning a single list of videos
    RETURN_NAMES = ("video_list",)
    OUTPUT_IS_LIST = (False,)  # Wrap the list as one element (not multiple outputs)

    FUNCTION = "collect"
    CATEGORY = "QS/video"

    def collect(self, videos):
        # videos is a list of (N,3,H,W) tensors
        return (videos,)  # Just wrap it once and send


class QsSimpleNSequential:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "video": ("IMAGE",),  # (b, c, h, w)
                "N": ("INT", {"default": 81, "min": 1}),
            }
        }

    RETURN_TYPES = ("IMAGE", "INT", "INT")  # list, append count, batch count
    RETURN_NAMES = ("video_list", "append_frames", "num_batches")

    OUTPUT_IS_LIST = (True, False, False)
    FUNCTION = "make_batches"
    CATEGORY = "QS/video"

    def make_batches(self, video, N):
        device = video.device
        b, c, h, w = video.shape

        append_frames = (N - (b % N)) % N  # Only if not divisible
        if append_frames > 0:
            pad_frame = video[-1:].repeat(append_frames, 1, 1, 1)
            video = torch.cat([video, pad_frame], dim=0)

        video_list = []
        for i in range(0, video.shape[0], N):
            chunk = video[i:i+N]
            video_list.append(chunk.to(device))  # (N, c, h, w)

        num_batches = len(video_list)
        return video_list, append_frames, num_batches


class QsSimpleVideoMerge:
    @classmethod
    def INPUT_TYPES(cls):
        return {
            "required": {
                "video_list": ("IMAGE",),  # list of (N, c, h, w)
                "append_frames": ("INT", {"default": 0, "min": 0}),
            }
        }

    RETURN_TYPES = ("IMAGE",)
    RETURN_NAMES = ("video",)
    FUNCTION = "combine"
    CATEGORY = "QS/video"

    def combine(self, video_list, append_frames):
  
        if append_frames > 0:
            video_list[-1] = video_list[-1][:-append_frames]

        # Concatenate all chunks along frame axis
        full_video = torch.cat(video_list, dim=0)

        return (full_video,)



NODE_CLASS_MAPPINGS = {
    "QSMaskPaddingNode": QSMaskPaddingNode,
    "QsSelectNframes": QsSelectNframes,
    "QsJoinFrames": QsJoinFrames,
    "QsNSequence": QsNSequence,
    "QsExtractFromList": QsExtractFromList,
    "QsVideoListCombine": QsVideoListCombine,
    "QsCollectToList": QsCollectToList,
    "QsSimpleNSequential": QsSimpleNSequential,
    "QsSimpleVideoMerge": QsSimpleVideoMerge,
}


NODE_DISPLAY_NAME_MAPPINGS = {
    "QSMaskPaddingNode": "QS Mask Padding",
    "QsSelectNframes": "Qs Select N Frames",
    "QsJoinFrames": "Qs Join Frames",
    "QsNSequence": "Qs N Sequence",
    "QsExtractFromList": "Qs Extract From List",
    "QsVideoListCombine": "Qs Video List Combine",
    "QsCollectToList": "Qs Collect To List",
    "QsSimpleNSequential": "Qs Simple N Sequential",
    "QsSimpleVideoMerge": "Qs Simple Video Merge"

}
