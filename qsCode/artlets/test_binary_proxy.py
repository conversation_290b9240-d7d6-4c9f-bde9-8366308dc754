#!/usr/bin/env python3
"""
Test script for Artlet binary message proxying functionality
"""

import json
import struct
import logging

# Configure logging
logging.basicConfig(level=logging.DEBUG)

def test_binary_message_parsing():
    """Test parsing of ComfyUI binary messages"""
    print("Testing ComfyUI binary message parsing...")

    # Create a mock ComfyUI binary message
    # Format: [4-byte event type][binary data]

    # Test 1: Preview image message
    event_type = 1  # BinaryEventTypes.PREVIEW_IMAGE
    image_data = b'\x89PNG\r\n\x1a\n' + b'mock_image_data' * 100  # Mock PNG header + data

    # Construct the ComfyUI binary message
    event_bytes = struct.pack('>I', event_type)  # Big-endian unsigned int
    comfyui_message = event_bytes + image_data
    
    print(f"Created ComfyUI binary message:")
    print(f"  Total length: {len(comfyui_message)} bytes")
    print(f"  Event type: {event_type}")
    print(f"  Binary data length: {len(image_data)} bytes")
    print(f"  Binary data starts with: {image_data[:20]}")

    # Test parsing logic (similar to what's in artlet_websocket_proxy.py)
    try:
        if len(comfyui_message) >= 4:
            # Decode the ComfyUI binary message format
            event_bytes = comfyui_message[:4]
            binary_data = comfyui_message[4:]
            parsed_event_type = struct.unpack(">I", event_bytes)[0]

            print(f"  Parsed event type: {parsed_event_type}")
            print(f"  Extracted binary data length: {len(binary_data)} bytes")
            print(f"  Binary data starts with: {binary_data[:20]}")

            # Verify the parsing worked correctly
            if parsed_event_type == event_type and binary_data == image_data:
                print("✅ ComfyUI binary message parsing successful")
                return True
            else:
                print("❌ ComfyUI binary message parsing failed - data mismatch")
                return False
        else:
            print("❌ Binary message too short")
            return False

    except Exception as e:
        print(f"❌ Error parsing binary message: {e}")
        return False


def test_message_transformation():
    """Test transformation of binary message metadata"""
    print("\nTesting binary message transformation...")
    
    # Mock transformer (simplified version)
    class MockTransformer:
        def __init__(self):
            self.local_prompt_id = "local_123"
            self.remote_prompt_id = "remote_456"
            self.node_id_mapping = {
                "remote_node_1": "local_node_A",
                "remote_node_2": "local_node_B"
            }
        
        def transform_message(self, event, data):
            transformed_data = data.copy()
            
            # Transform prompt_id
            if 'prompt_id' in transformed_data:
                if transformed_data['prompt_id'] == self.remote_prompt_id:
                    transformed_data['prompt_id'] = self.local_prompt_id
            
            # Transform node IDs
            if 'node' in transformed_data:
                remote_node_id = transformed_data['node']
                if remote_node_id in self.node_id_mapping:
                    transformed_data['node'] = self.node_id_mapping[remote_node_id]
            
            if 'display_node' in transformed_data:
                remote_display_node = transformed_data['display_node']
                if remote_display_node in self.node_id_mapping:
                    transformed_data['display_node'] = self.node_id_mapping[remote_display_node]
            
            return event, transformed_data
    
    # Test transformation
    transformer = MockTransformer()
    
    original_metadata = {
        "type": "executing",
        "data": {
            "node": "remote_node_1",
            "prompt_id": "remote_456",
            "display_node": "remote_node_1"
        }
    }
    
    event, transformed_data = transformer.transform_message(
        original_metadata["type"], 
        original_metadata["data"]
    )
    
    expected_data = {
        "node": "local_node_A",
        "prompt_id": "local_123",
        "display_node": "local_node_A"
    }
    
    print(f"Original data: {original_metadata['data']}")
    print(f"Transformed data: {transformed_data}")
    print(f"Expected data: {expected_data}")
    
    if transformed_data == expected_data:
        print("✅ Binary message transformation successful")
        return True
    else:
        print("❌ Binary message transformation failed")
        return False


def test_binary_message_reconstruction():
    """Test reconstruction of transformed binary messages"""
    print("\nTesting binary message reconstruction...")
    
    # Original message components
    original_metadata = {
        "type": "executing",
        "data": {
            "node": "remote_node_1",
            "prompt_id": "remote_456"
        }
    }
    
    transformed_metadata = {
        "type": "executing",
        "data": {
            "node": "local_node_A",
            "prompt_id": "local_123"
        }
    }
    
    binary_data = b'mock_binary_data' * 50
    
    # Create original structured message
    original_json = json.dumps(original_metadata).encode('utf-8')
    original_length = len(original_json)
    original_message = struct.pack('<I', original_length) + original_json + binary_data
    
    # Simulate transformation and reconstruction
    transformed_json = json.dumps(transformed_metadata).encode('utf-8')
    new_length = len(transformed_json)
    new_length_bytes = struct.pack('<I', new_length)
    reconstructed_message = new_length_bytes + transformed_json + binary_data
    
    print(f"Original message length: {len(original_message)} bytes")
    print(f"Reconstructed message length: {len(reconstructed_message)} bytes")
    
    # Verify the reconstructed message can be parsed correctly
    try:
        # Parse the reconstructed message
        length_bytes = reconstructed_message[:4]
        json_length = struct.unpack('<I', length_bytes)[0]
        json_part = reconstructed_message[4:4+json_length]
        binary_part = reconstructed_message[4+json_length:]
        
        parsed_metadata = json.loads(json_part.decode('utf-8'))
        
        print(f"Parsed metadata from reconstructed message: {parsed_metadata}")
        print(f"Binary data preserved: {binary_part == binary_data}")
        
        if parsed_metadata == transformed_metadata and binary_part == binary_data:
            print("✅ Binary message reconstruction successful")
            return True
        else:
            print("❌ Binary message reconstruction failed")
            return False
            
    except Exception as e:
        print(f"❌ Error parsing reconstructed message: {e}")
        return False


def test_progress_text_binary_message():
    """Test progress text binary message transformation"""
    print("\nTesting progress text binary message transformation...")

    # Create a progress text binary message
    # Format: [4-byte event type][4-byte node_id_length][node_id_string][text_string]
    # event_type = 3  # BinaryEventTypes.PROGRESS_TEXT
    remote_node_id = "remote_node_123"
    progress_text = "Processing step 5/10"

    # Construct the progress text binary data
    node_id_bytes = remote_node_id.encode('utf-8')
    text_bytes = progress_text.encode('utf-8')
    node_id_length = len(node_id_bytes)

    # Build the binary data part
    binary_data = struct.pack(">I", node_id_length) + node_id_bytes + text_bytes

    # Build the complete ComfyUI binary message
    event_bytes = struct.pack(">I", event_type)
    progress_message = event_bytes + binary_data

    print(f"Created progress text message:")
    print(f"  Event type: {event_type}")
    print(f"  Remote node ID: {remote_node_id}")
    print(f"  Progress text: {progress_text}")
    print(f"  Total message length: {len(progress_message)} bytes")

    # Test parsing and transformation
    try:
        # Parse the message
        if len(progress_message) >= 4:
            event_bytes = progress_message[:4]
            binary_data = progress_message[4:]
            parsed_event_type = struct.unpack(">I", event_bytes)[0]

            # if parsed_event_type == 3:  # PROGRESS_TEXT
                if len(binary_data) >= 4:
                    node_id_length = struct.unpack(">I", binary_data[:4])[0]
                    if node_id_length > 0 and len(binary_data) >= 4 + node_id_length:
                        node_id_bytes = binary_data[4:4+node_id_length]
                        text_bytes = binary_data[4+node_id_length:]

                        # Decode
                        parsed_node_id = node_id_bytes.decode('utf-8')
                        parsed_text = text_bytes.decode('utf-8')

                        print(f"  Parsed node ID: {parsed_node_id}")
                        print(f"  Parsed text: {parsed_text}")

                        # Transform node ID (simulate mapping)
                        node_id_mapping = {"remote_node_123": "local_node_456"}
                        local_node_id = node_id_mapping.get(parsed_node_id, parsed_node_id)

                        # Reconstruct with transformed node ID
                        local_node_id_bytes = local_node_id.encode('utf-8')
                        new_node_id_length = len(local_node_id_bytes)
                        new_binary_data = struct.pack(">I", new_node_id_length) + local_node_id_bytes + text_bytes

                        print(f"  Transformed node ID: {local_node_id}")
                        print(f"  New binary data length: {len(new_binary_data)} bytes")

                        if parsed_node_id == remote_node_id and parsed_text == progress_text and local_node_id == "local_node_456":
                            print("✅ Progress text binary message transformation successful")
                            return True
                        else:
                            print("❌ Progress text transformation failed")
                            return False

        print("❌ Failed to parse progress text message")
        return False

    except Exception as e:
        print(f"❌ Error processing progress text message: {e}")
        return False


def test_binary_event_types():
    """Test ComfyUI binary event types"""
    print("\nTesting ComfyUI binary event types...")

    # Test the standard ComfyUI binary event types
    binary_event_types = {
        'PREVIEW_IMAGE': 1,
        'UNENCODED_PREVIEW_IMAGE': 2,
    }

    all_passed = True
    for name, expected_value in binary_event_types.items():
        print(f"✅ {name} = {expected_value}")

    # Test that we can encode/decode event types correctly
    for event_type in [1, 2, 3]:
        try:
            # Encode
            event_bytes = struct.pack(">I", event_type)
            # Decode
            decoded_event = struct.unpack(">I", event_bytes)[0]

            if decoded_event == event_type:
                print(f"✅ Event type {event_type} encode/decode successful")
            else:
                print(f"❌ Event type {event_type} encode/decode failed")
                all_passed = False
        except Exception as e:
            print(f"❌ Error with event type {event_type}: {e}")
            all_passed = False

    return all_passed


if __name__ == "__main__":
    print("=== Artlet Binary Message Proxy Tests ===")
    
    tests = [
        test_binary_message_parsing,
        test_message_transformation,
        test_binary_message_reconstruction,
        test_progress_text_binary_message,
        test_binary_event_types
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n=== Test Results: {passed}/{total} tests passed ===")
    
    if passed == total:
        print("🎉 All binary message proxy tests passed!")
    else:
        print("⚠️  Some tests failed. Check the implementation.")
