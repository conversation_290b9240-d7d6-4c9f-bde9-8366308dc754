comfy update readme
-------------------
From 0.3.24 to 0.3.40
## Upgrade work

# Following files requires modifications to call additional functionality call from qsCode/comfy
main.py
server.py
nodes.py
cli_args.py
execution.py

# Following file make a qs version with torch commented and extra qs pips
requirements.txt

everytime a new requirement comes copy that as qs_requirements.txt comment torch line so that qs environment is created fresh
and comment all the torch lines

Then run
#torch installs with command from recreate_envs.sh
Then run
pip install -r ./qsCode/qs_reqs/qs_requirements.txt

Now fix any other additional packages required after upgrade
Once done run 

pip freeze > ./qsCode/qs_reqs/temp_frozen_reqs.txt

compare and correct frozen_reqs.txt   and then remove temp_frozen_reqs.txt
