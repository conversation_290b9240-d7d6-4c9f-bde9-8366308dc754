from aiohttp import web
import uuid
import execution

from qsCode.artlets.artlet_models import ArtletData,extract_auth_headers
from qsCode.artlets.artlet_helpers import (process_artlet_prompt_generators,
     get_or_create_client, get_or_create_prompt,ArtletRoutes)
def qs_prompt_server_init(self):
    self.artlet_routes = ArtletRoutes(self)
    self.temp_store = {}
    self.artlet_data = ArtletData()


def qs_process_artlet_prompt(self,request,json_data, prompt, valid, prompt_id, client_id):
    
    artlet_process_data = process_artlet_prompt_generators(prompt, client_id)
    web_response = None
    if artlet_process_data['save_prompts'] == True: # required work is already done no need run further communicate to client via exception
        self.send_sync("execution_error", artlet_process_data['mes'], client_id)
        web_response = web.json_response({"error": artlet_process_data['mes']['exception_message'], "node_errors": []}, status=200)
    else :
        prompt = artlet_process_data['client_prompt']
        valid = execution.validate_prompt(prompt)
        
        # Process Artlet-specific data
        artlet_server_nodes, artlet_client_nodes = self.artlet_routes.handle_artlet_prompt_data(json_data, prompt, client_id)
        
        # Extract auth headers for Artlet client nodes
        if len(artlet_client_nodes) > 0:
            extract_auth_headers(request, self.artlet_data.as_client[client_id])
        
        self.artlet_routes.setup_artlet_prompt_data(prompt, client_id, prompt_id, artlet_server_nodes, artlet_client_nodes)
    return web_response, prompt, valid


def qs_prompt_server_ws_init(self,request):           
        client_id = request.query.get('clientId', str(uuid.uuid4()))
        print('got client id', client_id)
        if client_id is None:
            raise Exception("No client id provided")
        
        # Store authentication headers in the client data
        client_data = get_or_create_client(self.artlet_data, client_id)

        # Extract and store authentication headers
        auth_headers = {}
        for header in ['X-Auth-Token', 'X-Auth-User', 'X-Auth-Email', 'Cookie']:
            if header in request.headers:
                auth_headers[header] = request.headers[header]
        #merge client_data.auth_headers and auth_headers
        auth_headers.update(client_data.auth_headers)
        # Store the updated auth headers back to client_data
        client_data.auth_headers = auth_headers
