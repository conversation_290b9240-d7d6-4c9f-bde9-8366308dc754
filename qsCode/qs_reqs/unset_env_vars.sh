#!/bin/bash

# Remove qs1 site-packages from the end of PYTHONPATH
QS_PYTHONPATH="/home/<USER>/.conda/envs/qsbase/lib/python3.12/site-packages"
if [[ "$PYTHONPATH" == *":$QS_PYTHONPATH" ]]; then
    export PYTHONPATH="${PYTHONPATH%:$QS_PYTHONPATH}"
elif [[ "$PYTHONPATH" == "$QS_PYTHONPATH" ]]; then
    unset PYTHONPATH
fi

# Remove qs1 bin from the end of PATH
QS_BIN_PATH="/home/<USER>/.conda/envs/qsbase/bin"
if [[ "$PATH" == *":$QS_BIN_PATH" ]]; then
    export PATH="${PATH%:$QS_BIN_PATH}"
elif [[ "$PATH" == "$QS_BIN_PATH" ]]; then
    unset PATH
fi
