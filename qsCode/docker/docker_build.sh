#This file builds docker image. 
#Tags used so that it doesnt affect existing

#qsComfy:prod_latest
#qsComfy:dev_latest
#qsComfy:latest (on the machine)
#qsComfy:comfy_ver.n/s/h.d
#qsComfy:comfy_ver.dt   #frozen version
#docker tag source_tag dest_tag

#!/bin/bash

ACR_NAME="qsacr001"
LABEL=$1
IMAGE=${2:-qs-comfy}
script_dir=$(dirname "$0")
#if label includeslatest word reject it also if no label is given exit
if [ -z "$LABEL" ]; then
    echo "LABEL is not given"
    echo "usage docker_build.sh LABEL [IMAGE]"
    exit 1
fi

if [[ $LABEL == *"latest"* ]]; then
    echo "latest tag is not allowed during docker_build.sh ; use explicit docker_tag.sh instead"
    exit 1
fi

docker build --build-arg HOST_UID=$(id -u) --build-arg HOST_GID=$(id -g)  -f $script_dir/Dockerfile -t $ACR_NAME.azurecr.io/$IMAGE:$LABEL .



