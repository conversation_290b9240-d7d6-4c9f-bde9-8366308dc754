import json
import copy
import os

def split_artlet_prompts(prompt, server_node_list, client_block_id="client_block_1",
                      server_block_id="server_block_1", server_address="http://localhost:8188",
                      single_block_prompt=True, server_api_file="server_prompt.json",
                      client_api_file="client_prompt.json", sequence_id=0, update_int=0, run_nodes_on_server=False):
    """
    Split an artlet prompt JSON into client and server prompts.

    Args:
        prompt (dict or str): The original prompt JSON or path to JSON file
        server_node_list (list or str): List of node IDs to move to the server prompt, or comma-separated string
        client_block_id (str): ID for the client block in the client prompt
        server_block_id (str): ID for the server blocks in both prompts
        server_address (str): Address of the server for the client to connect to
        single_block_prompt (bool): Whether to use a single block prompt
        server_api_file (str): Path to save the server prompt JSON (relative to client)
        client_api_file (str): Path to save the client prompt JSON
        update_int (int): Update integer to force rerun

    Returns:
        tuple: (client_prompt, server_prompt) as dictionaries
    """
    # Load the prompt if it's a string (file path)
    if isinstance(prompt, str):
        with open(prompt, 'r') as f:
            prompt = json.load(f)

    # Convert server_node_list to list if it's a string
    if isinstance(server_node_list, str):
        server_node_list = [node.strip() for node in server_node_list.split(',')]

    # Make deep copies to avoid modifying the original
    client_prompt = prompt
    server_prompt = {}

    # Find the highest node ID to generate new IDs
    max_node_id = 0
    for node_id in prompt.keys():
        if node_id.isdigit() and int(node_id) > max_node_id:
            max_node_id = int(node_id)

    # Create new node IDs
    artlet_client_block_id = str(max_node_id + sequence_id*4+1)
    server_input_block_id = str(max_node_id + sequence_id*4+2)
    server_output_block_id = str(max_node_id + sequence_id*4+3)

    # Convert server_node_list to strings if they're integers
    server_node_list = [str(node) for node in server_node_list]

    # Validate that all server nodes exist in the prompt
    for node_id in server_node_list:
        if node_id not in prompt:
            raise ValueError(f"Server node {node_id} not found in the prompt")

    # Analyze connections
    # 1. Find inputs to server nodes from client nodes
    server_inputs = {}  # {server_node: {input_name: [source_node, output_slot]}}
    # 2. Find outputs from server nodes to client nodes
    server_outputs = {}  # {client_node: {input_name: [server_node, output_slot]}}

    # Collect all connections
    for node_id, node_data in prompt.items():
        if 'inputs' not in node_data:
            continue

        for input_name, input_value in node_data['inputs'].items():
            # Check if this is a connection (list with node ID and output slot)
            if isinstance(input_value, list) and len(input_value) == 2:
                source_node, output_slot = input_value
                source_node = str(source_node)

                # If this node is a server node and the source is not, record as server input
                if node_id in server_node_list and source_node not in server_node_list:
                    if node_id not in server_inputs:
                        server_inputs[node_id] = {}
                    server_inputs[node_id][input_name] = [source_node, output_slot]

                # If this node is not a server node and the source is, record as server output
                elif node_id not in server_node_list and source_node in server_node_list:
                    if node_id not in server_outputs:
                        server_outputs[node_id] = {}
                    server_outputs[node_id][input_name] = [source_node, output_slot]

    # Create server prompt
    # Add server nodes to server prompt
    for node_id in server_node_list:
        if node_id in prompt:
            server_prompt[node_id] = copy.deepcopy(prompt[node_id])

    # Create server input block
    server_input_block = {
        "inputs": {
            "server_block_id": server_block_id,
            "server_block_name": "server block 1",
            "update_int": update_int
        },
        "class_type": "ArtletServerInputBlock",
        "_meta": {
            "title": "Artlet Server Input Block"
        }
    }

    # Add input slots to server input block
    input_slot = 0
    for node_id, inputs in server_inputs.items():
        for input_name, (source_node, output_slot) in inputs.items():
            if input_slot < 10:  # ArtletServerInputBlock supports up to 10 inputs
                server_input_block["inputs"][f"input_name_{input_slot}"] = input_name
                # In server prompt, connect server input block to server nodes
                if node_id in server_prompt and input_name in server_prompt[node_id]["inputs"]:
                    server_prompt[node_id]["inputs"][input_name] = [server_input_block_id, input_slot]
                input_slot += 1

    # Create server output block
    server_output_block = {
        "inputs": {
            "server_block_id": server_block_id,
            "server_block_name": "output block",
            "prev_server_block_id": [server_input_block_id, 11],  # Connect to server input block
            "update_int": update_int
        },
        "class_type": "ArtletServerOutputBlock",
        "_meta": {
            "title": "Artlet Server Output Block"
        }
    }

    # Add output slots to server output block
    output_mapping = {}  # Maps [server_node, output_slot] to output_slot in server output block
    output_slot = 0

    for client_node, inputs in server_outputs.items():
        for input_name, (server_node, src_output_slot) in inputs.items():
            key = f"{server_node}_{src_output_slot}"
            if key not in output_mapping and output_slot < 10:  # ArtletServerOutputBlock supports up to 10 outputs
                output_mapping[key] = output_slot
                server_output_block["inputs"][f"output_name_{output_slot}"] = f"{server_node}_{input_name}"
                server_output_block["inputs"][f"output_{output_slot}"] = [server_node, src_output_slot]
                output_slot += 1

    # Add server input and output blocks to server prompt
    server_prompt[server_input_block_id] = server_input_block
    server_prompt[server_output_block_id] = server_output_block

    # Create client prompt
    # Remove server nodes from client prompt
    for node_id in server_node_list:
        if node_id in client_prompt:
            del client_prompt[node_id]

    # Create client block
    client_block = {
        "inputs": {
            "client_block_id": client_block_id,
            "server_block_id": server_block_id,
            "server_address": server_address,
            "single_block_prompt": single_block_prompt,
            "api_file": server_api_file,  # Path to the server API file
            "update_int": update_int
        },
        "class_type": "ArtletClientBlock",
        "_meta": {
            "title": "Artlet Client Block"
        }
    }

    # Note: client_api_file is used as the output path for the client prompt
    # but not directly in the client block configuration

    # Add input slots to client block
    input_slot = 0
    for node_id, inputs in server_inputs.items():
        for input_name, (source_node, output_slot) in inputs.items():
            if input_slot < 10:  # ArtletClientBlock supports up to 10 inputs
                client_block["inputs"][f"input_{input_slot}"] = [source_node, output_slot]
                input_slot += 1
    #add run_nodes_on_server to client block
    client_block["inputs"]["run_nodes_on_server"] = run_nodes_on_server
    # Add client block to client prompt
    client_prompt[artlet_client_block_id] = client_block

    # Update connections in client prompt
    for client_node, inputs in server_outputs.items():
        for input_name, (server_node, src_output_slot) in inputs.items():
            key = f"{server_node}_{src_output_slot}"
            if key in output_mapping and client_node in client_prompt and input_name in client_prompt[client_node]["inputs"]:
                output_slot = output_mapping[key]
                client_prompt[client_node]["inputs"][input_name] = [artlet_client_block_id, output_slot]

    return client_prompt, server_prompt

def save_prompt(prompt, output_path):
    """
    Split an artlet prompt and save the resulting client and server prompts to files.

    Args:
        prompt (dict or str): The original prompt JSON or path to JSON file
        path    
    Returns:
        tuple: (client_output_path, server_output_path)
    """
    if prompt is None or output_path in [None, ''] :  # no action return 
        return None
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w') as f:
            json.dump(prompt, f, indent=4)
    return output_path

def analyze_prompt_connections(prompt):
    """
    Analyze the connections between nodes in a prompt.

    Args:
        prompt (dict): The prompt JSON

    Returns:
        dict: A dictionary with node connections information
    """
    connections = {
        "node_types": {},
        "inputs": {},
        "outputs": {},
    }

    # Collect node types
    for node_id, node_data in prompt.items():
        if "class_type" in node_data:
            connections["node_types"][node_id] = node_data["class_type"]

    # Collect connections
    for node_id, node_data in prompt.items():
        if "inputs" not in node_data:
            continue

        connections["inputs"][node_id] = {}

        for input_name, input_value in node_data["inputs"].items():
            if isinstance(input_value, list) and len(input_value) == 2:
                source_node, output_slot = input_value
                source_node = str(source_node)

                connections["inputs"][node_id][input_name] = [source_node, output_slot]

                # Record as output of source node
                if source_node not in connections["outputs"]:
                    connections["outputs"][source_node] = {}

                if output_slot not in connections["outputs"][source_node]:
                    connections["outputs"][source_node][output_slot] = []

                connections["outputs"][source_node][output_slot].append([node_id, input_name])

    return connections

def print_prompt_summary(prompt, title="Prompt Summary"):
    """
    Print a summary of the prompt.

    Args:
        prompt (dict): The prompt JSON
        title (str): Title for the summary
    """
    connections = analyze_prompt_connections(prompt)

    print(f"\n{title}")
    print("=" * len(title))
    print(f"Total nodes: {len(prompt)}")

    # Print node types
    print("\nNode Types:")
    for node_id, node_type in connections["node_types"].items():
        print(f"  Node {node_id}: {node_type}")

    # Print connections
    print("\nConnections:")
    for node_id, inputs in connections["inputs"].items():
        if inputs:
            print(f"  Node {node_id} ({connections['node_types'].get(node_id, 'Unknown')}) inputs:")
            for input_name, (source_node, output_slot) in inputs.items():
                source_type = connections["node_types"].get(source_node, "Unknown")
                print(f"    {input_name} <- Node {source_node} ({source_type}) output slot {output_slot}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Split an artlet prompt into client and server prompts')
    parser.add_argument('prompt', help='Path to the prompt JSON file')
    parser.add_argument('server_nodes', help='Comma-separated list of node IDs to move to the server prompt')
    parser.add_argument('--client-output', default='client_prompt.json', help='Path to save the client prompt')
    parser.add_argument('--server-output', default='server_prompt.json', help='Path to save the server prompt')
    parser.add_argument('--verbose', '-v', action='store_true', help='Print detailed information about the prompts')

    # Add additional command line arguments
    parser.add_argument('--client-block-id', default='client_block_1', help='ID for the client block')
    parser.add_argument('--server-block-id', default='server_block_1', help='ID for the server blocks')
    parser.add_argument('--server-address', default='http://localhost:8188', help='Address of the server')
    parser.add_argument('--single-block-prompt', action='store_true', default=True, help='Use single block prompt')
    parser.add_argument('--server-api-file', default='server_prompt.json', help='Path to server API file')
    parser.add_argument('--client-api-file', default='client_prompt.json', help='Path to client API file')
    parser.add_argument('--update-int', type=int, default=0, help='Update integer to force rerun')

    args = parser.parse_args()

    server_node_list = [node.strip() for node in args.server_nodes.split(',')]

    # Load the original prompt
    with open(args.prompt, 'r') as f:
        original_prompt = json.load(f)

    # Print original prompt summary if verbose
    if args.verbose:
        print_prompt_summary(original_prompt, f"Original Prompt: {args.prompt}")

    # Split the prompt
    client_prompt, server_prompt = split_artlet_prompts(
        original_prompt, server_node_list,
        args.client_block_id, args.server_block_id,
        args.server_address, args.single_block_prompt,
        args.server_api_file, args.client_api_file, 0, args.update_int
    )
    client_path = 'artlet/'+args.client_api_file 
    server_path = 'artlet/' + args.server_api_file
    # Save the split prompts
    server_path = save_prompt(
        server_prompt, 
        server_path
    )

    print(f"\nClient prompt saved to {client_path}")
    print(f"Server prompt saved to {server_path}")

    # Print split prompt summaries if verbose
    if args.verbose:
        print_prompt_summary(client_prompt, f"Client Prompt: {client_path}")
        print_prompt_summary(server_prompt, f"Server Prompt: {server_path}")

        print("\nSplit Summary:")
        print(f"  Original prompt: {len(original_prompt)} nodes")
        print(f"  Client prompt: {len(client_prompt)} nodes")
        print(f"  Server prompt: {len(server_prompt)} nodes")
        print(f"  Server nodes: {', '.join(server_node_list)}")

        # Find the ArtletClientBlock in client prompt
        client_block_id = None
        for node_id, node_data in client_prompt.items():
            if node_data.get("class_type") == "ArtletClientBlock":
                client_block_id = node_id
                break

        if client_block_id:
            print(f"\nArtletClientBlock added as node {client_block_id}")

        # Find the ArtletServerInputBlock and ArtletServerOutputBlock in server prompt
        server_input_id = None
        server_output_id = None
        for node_id, node_data in server_prompt.items():
            if node_data.get("class_type") == "ArtletServerInputBlock":
                server_input_id = node_id
            elif node_data.get("class_type") == "ArtletServerOutputBlock":
                server_output_id = node_id

        if server_input_id:
            print(f"ArtletServerInputBlock added as node {server_input_id}")
        if server_output_id:
            print(f"ArtletServerOutputBlock added as node {server_output_id}")
