from qsCode.artlets.artlets_new import ArtletServerInputBlock, ArtletServerOutputBlock, \
  ArtletClientBlock, ArtletDescription, ArtletPromptGenerator

NODE_CLASS_MAPPINGS = {
    "ArtletServerInputBlock": ArtletServerInputBlock,
    "ArtletServerOutputBlock": ArtletServerOutputBlock,
    "ArtletClientBlock": Artlet<PERSON>lientBlock,
    "ArtletDescription": ArtletDescription,
    'ArtletPromptGenerator': ArtletPromptGenerator,
    
  }
NODE_DISPLAY_NAME_MAPPINGS = {
    "ArtletServerInputBlock": "Artlet Server Input Block",
    "ArtletServerOutputBlock": "Artlet Server Output Block",
    "ArtletClientBlock": "Artlet Client Block",
    "ArtletDescription": "Artlet Description",
    'ArtletPromptGenerator': 'Artlet Prompt Generator',
  }

