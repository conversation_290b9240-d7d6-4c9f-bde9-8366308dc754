#!/bin/bash

# Get the absolute path of the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$SCRIPT_DIR/../../"
CACHE_LIST="$BASE_DIR/config/cache_models.txt"
SRC_BASE="/qsfs2/services/qsComfy/models"
DEST_BASE="/home/<USER>/model_cache"

# Check if the cache list file exists
if [[ ! -f "$CACHE_LIST" ]]; then
    echo "Error: $CACHE_LIST not found"
    exit 1
fi

while IFS= read -r REL_PATH || [[ -n "$REL_PATH" ]]; do
    # Strip leading/trailing whitespace
    REL_PATH="$(echo "$REL_PATH" | xargs)"
    echo "Processing: $REL_PATH"
    # Skip empty or comment lines
    if [[ -z "$REL_PATH" || "$REL_PATH" == \#* ]]; then
        continue
    fi

    SRC_FILE="$SRC_BASE/$REL_PATH"
    DEST_DIR="$DEST_BASE/$(dirname "$REL_PATH")"
    DEST_FILE="$DEST_DIR/ch_$(basename "$REL_PATH")"

    # Ensure source exists
    if [[ ! -f "$SRC_FILE" ]]; then
        echo "Warning: Source file not found: $SRC_FILE"
        continue
    fi

    # Create destination directory if needed
    mkdir -p "$DEST_DIR"

    # Copy if not present or outdated
    if [[ ! -f "$DEST_FILE" || "$SRC_FILE" -nt "$DEST_FILE" ]]; then
        echo "Copying: $SRC_FILE -> $DEST_FILE"
        cp "$SRC_FILE" "$DEST_FILE"
    else
        echo "Up to date: $DEST_FILE"
    fi
done < "$CACHE_LIST"
