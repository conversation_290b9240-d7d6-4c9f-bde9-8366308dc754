import logging
import os
import time 

from numpy import byte
import json
import urllib.request
import urllib.parse
import uuid
import random
import base64
import pickle
import server
import websocket #type: ignore
import json
import comfy.model_management
import comfy.utils


client_id = str(uuid.uuid4())
     
# class AnyType(str):
#     """A special class that is always equal in not equal comparisons. Credit to pythongosssss"""

#     def __eq__(self, _) -> bool:
#         return True

#     def __ne__(self, __value: object) -> bool:
#         return False

# any = AnyType("*")

# class ArtletServerInputBlock: 
#     def __init__(self):
#         pass
#     # @classmethod
#     # def IS_CHANGED(s, kwargs):
#     #     return time.time()
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {"required": {
#             "server_block_id": ("STRING",),
#             },
                
#             "optional": {
#                "server_block_name": ("STRING",{ "default": ""}),
#                 "prev_server_block_id": ("STRING",{ "default": None,  "forceInput": True,}),
                
#                 "input_0": (any, {"default": None}), "input_name_0": ("STRING", {"default": ""}),
#                 "input_1": (any, {"default": None}), "input_name_1": ("STRING", {"default": ""}),
#                 "input_2": (any, {"default": None}), "input_name_2": ("STRING", {"default": ""}),
#                 "input_3": (any, {"default": None}), "input_name_3": ("STRING", {"default": ""}),
#                 "input_4": (any, {"default": None}), "input_name_4": ("STRING", {"default": ""}),
#                 "input_5": (any, {"default": None}), "input_name_5": ("STRING", {"default": ""}),
#                 "input_6": (any, {"default": None}), "input_name_6": ("STRING", {"default": ""}),
#                 "input_7": (any, {"default": None}), "input_name_7": ("STRING", {"default": ""}),
#                 "input_8": (any, {"default": None}), "input_name_8": ("STRING", {"default": ""}),
#                 "input_9": (any, {"default": None}), "input_name_9": ("STRING", {"default": ""}),
#                 "update_int": ("INT",{"default": 0}), 
#             },
#         }
#     # return types will be all input_nodes, all_input_names, all_output_names, api_file, block_id
#     RETURN_NAMES = ('input_0', 'input_1', 'input_2', 'input_3', 'input_4', 'input_5', 
#                     'input_6', 'input_7', 'input_8', 'input_9', 
#                     'input_names',  'server_block_id')
#     RETURN_TYPES = (AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), 
#                     AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), "LIST", "STRING",)
#     FUNCTION = "artlet_server_block"
#     CATEGORY = "QS/qslets"
#     OUTPUT_NODE = True
    
#     # @classmethod
#     # def IS_CHANGED(s, kwargs):
#     #     return time.time()
    
#     def artlet_server_block(
#                             self, server_block_id,server_block_name='', prev_server_block_id=None,                             
#                             input_0=None, input_name_0='', input_1=None, input_name_1='',
#                             input_2=None, input_name_2='', input_3=None, input_name_3='',
#                             input_4=None, input_name_4='', input_5=None, input_name_5='',
#                             input_6=None, input_name_6='', input_7=None, input_name_7='',
#                             input_8=None, input_name_8='', input_9=None, input_name_9='',
#                             update_int=0
#                     ):
                           

#         print(f'got input_0 = {type(input_0)} input_name_0 = {input_name_0}')
#         input_names = [input_name_0, input_name_1, input_name_2, input_name_3, input_name_4, input_name_5, input_name_6, input_name_7, input_name_8, input_name_9]
#         server_instance = server.PromptServer.instance
#         prompt_id = server_instance.last_prompt_id
#         prompt_queue = server_instance.prompt_queue
#         print('artlet_server_store ', server_instance.artlet_server_store, 'in ArtletServerInputBlock')
#         artlet_service = server_instance.artlet_server_store.get('artlet_service', False) 
         
#         if artlet_service :
#             # Running as an artlet and input are not ready so wait for input add the code
#             # add the code to use client_inputs as input
            
#             input_status = prompt_queue.get_server_object_status(prompt_id,server_block_id,'input')
#             pbar = comfy.utils.ProgressBar(1000)
#             progress = 0
#             while input_status != 'ready':
#                 if progress % 1000 == 0:
#                     print(f'sending throw_exception_if_processing_interrupted input_status {progress}')
#                 comfy.model_management.throw_exception_if_processing_interrupted()
#                 pbar.update_absolute(progress % 1000, total=1000)
#                 time.sleep(0.1)
#                 progress += 1
#                 input_status = prompt_queue.get_server_object_status(prompt_id,server_block_id,'input')
            
            
#             [client_input_0, client_input_1, client_input_2, client_input_3, client_input_4, client_input_5,
#              client_input_6, client_input_7, client_input_8, client_input_9] = [None] * 10
#             input_objects = prompt_queue.get_binary_server_objects(prompt_id,server_block_id,'input')
#             [client_input_0, client_input_1, client_input_2, client_input_3, client_input_4, client_input_5,
#              client_input_6, client_input_7, client_input_8, client_input_9] = [ v.get('obj',None) for v in input_objects ] # expecting these to be 10 and in order
#             return (
#                     client_input_0, client_input_1, client_input_2, client_input_3, client_input_4, client_input_5,
#                     client_input_6, client_input_7, client_input_8, client_input_9,
#                     input_names, server_block_id
#             )
#         else:
#             return ( input_0, input_1, input_2, input_3, input_4, input_5, input_6, input_7, input_8, input_9, 
#                     input_names, server_block_id
#             ) 

# class ArtletServerOutputBlock:
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return { "required": {"server_block_id": ("STRING",)},
#                 "optional": {
#                     "server_block_name": ("STRING",{ "default": None,}),
#                     "prev_server_block_id": ("STRING",{ "default": None,  "forceInput": True,}),
                    
#                     "output_0": (any, {"default": None}), "output_1": (any, {"default": None}),
#                     "output_2": (any, {"default": None}), "output_3": (any, {"default": None}),
#                     "output_4": (any, {"default": None}), "output_5": (any, {"default": None}),
#                     "output_6": (any, {"default": None}), "output_7": (any, {"default": None}),
#                     "output_8": (any, {"default": None}), "output_9": (any, {"default": None}),
#                     "output_name_0": ("STRING",{ "default": ""}), "output_name_1": ("STRING",{ "default": ""}),
#                     "output_name_2": ("STRING",{ "default": ""}), "output_name_3": ("STRING",{ "default": ""}),
#                     "output_name_4": ("STRING",{ "default": ""}), "output_name_5": ("STRING",{ "default": ""}),
#                     "output_name_6": ("STRING",{ "default": ""}), "output_name_7": ("STRING",{ "default": ""}),
#                     "output_name_8": ("STRING",{ "default": ""}), "output_name_9": ("STRING",{ "default": ""}),
#                 },
#                 "hidden": {
#                     "update_int": ("INT",{"default": 0}),
#                 }
#         }
#     RETURN_TYPES = ("LIST", "STRING")
#     RETURN_NAMES = ("output_names", "server_block_id")
#     FUNCTION = "artlet_server_output_block"
#     CATEGORY = "Artlets"
#     OUTPUT_NODE = True
    
#     @classmethod
#     def IS_CHANGED(cls, kwargs):
#         return time.time()
    
#     def artlet_server_output_block(self, server_block_id, server_block_name = None, prev_server_block_id = None,
#                 output_0 = None, output_1 = None, output_2 = None, output_3 = None, output_4 = None, 
#                 output_5 = None, output_6 = None, output_7 = None, output_8 = None, output_9 = None,
#                 output_name_0 = '', output_name_1 = '', output_name_2 = '', output_name_3 = '', output_name_4 = '',
#                 output_name_5 = '', output_name_6 = '', output_name_7 = '', output_name_8 = '', output_name_9 = '',
#                 update_int = 0
#                 ):
#         output_names = [output_name_0, output_name_1, output_name_2, output_name_3, output_name_4, output_name_5,
#                                  output_name_6, output_name_7, output_name_8, output_name_9]
#         #remove empty comma strings
#         server_instance = server.PromptServer.instance
#         prompt_id = server_instance.last_prompt_id
#         prompt_queue = server_instance.prompt_queue
#         print('artlet_server_output_block prompt_id ',prompt_id,' server_block_id ',server_block_id)
#         print('artlet_server_store ', server_instance.artlet_server_store, 'in ArtletServerOutputBlock')
#         artlet_service = server_instance.artlet_server_store.get('artlet_service', False)
#         if artlet_service:
#             # service has run now needs the set output so that client can request and get it
#             for i in range(10):
#                 prompt_queue.add_binary_server_object(prompt_id, server_block_id, 'output', f'output_{i}', locals()[f'output_{i}'])
#             # now set the status to ready so that client can request and get it
#             prompt_queue.set_server_object_status(prompt_id, server_block_id, 'output', 'ready')
#             #print output block to see if its sent
#             # print('server data in store ',prompt_queue.server_data_store)
#             print('server object status in store ',prompt_queue.server_object_status)
            
#         else :  # this is local run do nothing
#             pass
        
#         return (output_names, server_block_id)
    
# class ArtletClientBlock: 
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {"required": {
#             "client_block_id": ("STRING",),
#                 "input_0": (any, {"default": None}),
#             },
                
#             "optional": {
#                 "server_block_id": ("STRING",{ "default": None}),
#                 "prompt_block_id": ("STRING",{ "default": None, "forceInput": True,}),
#                 "api_file": ("STRING",{ "default": None}),
#                 "server_address": ("STRING",{ "default": None}),
#                 "single_block_prompt": ("BOOLEAN", {"default": False}),
#                  "input_1": (any, {"default": None}),   
#                 "input_2": (any, {"default": None}), "input_3": (any, {"default": None}),
#                 "input_4": (any, {"default": None}), "input_5": (any, {"default": None}),
#                 "input_6": (any, {"default": None}), "input_7": (any, {"default": None}),
#                 "input_8": (any, {"default": None}), "input_9": (any, {"default": None}),
#                 "rerun_block_seed": ("INT", {"default": 0, "min": 0, "max": 0xffffffffffffffff,  
#                     "tooltip": "The use seed value to force run artlet on server."}),
#             },
            
#         }
#     RETURN_NAMES = ('output_0', 'output_1', 'output_2', 'output_3', 'output_4', 'output_5', 
#                     'output_6', 'output_7', 'output_8', 'output_9', 
#                     'client_block_id')
#     RETURN_TYPES = (AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"),
#                     AnyType("*"), AnyType("*"), AnyType("*"), AnyType("*"), "STRING",)
#     FUNCTION = "artlet_client"
#     CATEGORY = "QS/qslets"
#     OUTPUT_NODE = True
    
        
#     def artlet_client(
#                     self, client_block_id, input_0=None, prompt_block_id=None,server_block_id=None, api_file=None, server_address=None, single_block_prompt=False,
#                     input_1=None, input_2=None, input_3=None, input_4=None, input_5=None,
#                     input_6=None, input_7=None, input_8=None, input_9=None, rerun_block_seed=0
#             ):
        
                           
#         print(f'got client_block_id = {client_block_id} prompt_block_id = {prompt_block_id} api_file = {api_file}')
#         print(f'got input_0 = {type(input_0)} ')
#         if single_block_prompt:
#             prompt_block_id = None
#             assert api_file is not None, 'api_file is required for single block prompt' 
#         # first check if the prompt is triggered on server and there is associated websocket 
#         client_instance = server.PromptServer.instance
#         if api_file:
#                 api_file = os.path.join(os.path.dirname(__file__), '../artlet', api_file)
#         ws = self.get_websocket(client_instance, api_file, server_address, prompt_block_id)
#         server_prompt_id = self.get_server_prompt_id(client_instance, api_file, server_address, prompt_block_id)
#         print('got prompt', server_prompt_id, 'for', prompt_block_id)
#         ret = self.send_server_inputs(server_address, server_prompt_id, server_block_id, input_0, input_1, input_2,
#                                       input_3, input_4, input_5, input_6, input_7, input_8, input_9)
#         # now in a loop get progress and check if output are ready
        
#         output_data = self.get_outputs(server_address, server_prompt_id, server_block_id)
#         output_0, output_1, output_2, output_3, output_4, output_5, output_6, output_7, output_8, output_9  = output_data
#         #if this is single block prompt then clear prompt # keep the web socket that applies for entire clientside workflow
#         if single_block_prompt:
#             self.clear_server_prompt(client_instance, server_address, server_prompt_id)
        
#         return (output_0, output_1, output_2, output_3, output_4, output_5, output_6, output_7, output_8, output_9,
#             api_file, client_block_id)

#     def clear_server_prompt(self, client_instance, server_address, server_prompt_id):
#         for i in range (5):
#             try:
#                 print('sending clean up to server and interrupt ',i,'th time')
#                 #first interrupt the server 
#                 url = f"http://{server_address}/artlet/prompt_remove/{server_prompt_id}/"
#                 req = urllib.request.Request(url, method="POST")
#                 result = urllib.request.urlopen(req).read()
#                 print('result', result)
#             except Exception as e:
#                 logging.warning(f"Failed to send clean up to server: {e}")
#                 pass
#             time.sleep(0.1)
          
#         try:
#             ws = client_instance.artlet_client_store.get('websocket', None)
#             if ws is not None:
#                 print('closing websocket')
#                 ws.close()
#         except Exception as e:
#             print(f'error closing websocket {e}')
#             pass    
#         client_instance.artlet_client_store['websocket'] = None   
#         client_instance.artlet_client_store['server_prompt_id'] = None 
#         client_instance.artlet_client_store['server_address'] = None
#         print('cleanup_artlet_client_instance complete', client_instance.artlet_client_store)
#         return 
    
#     def get_websocket(self, client_instance, api_file, server_address, prompt_block_id):
#         #first check if websocket is already open and established
#         if prompt_block_id not in ['', None]:
#             #this block is dependent and shoud only return the websocket if it is already open
#             pbar = comfy.utils.ProgressBar(50)
#             progress = 0
#             for i in range(5):
#                 ws = client_instance.artlet_client_store.get('websocket', None)
#                 if ws:
#                     return ws
#                 if progress % 50 == 0:
#                     print(f'sending throw_exception_if_processing_interrupted websocket {progress}')
#                 comfy.model_management.throw_exception_if_processing_interrupted()
#                 pbar.update_absolute(progress % 50, total=50)
#                 time.sleep(0.01)
#                 progress += 1
#             if ws is None and api_file in [None, '']:  # neither api_file nor server_prompt_id is present
#                 raise Exception('error getting websocket')
#                     # This is prompt block it should open the websocket if not existing
#         if api_file is None or server_address is None:
#             print('this is first block so api_file and server_address should be present')
#             raise Exception('this is first block so api_file and server_address should be present')
#         ws = client_instance.artlet_client_store.get('websocket', None)
#         if ws:
#             # websocket is already open
#             return ws
#         try: 
#             ws = websocket.WebSocket() 
#             ws.connect("ws://{}/ws?clientId={}".format(server_address, client_id))
#         except Exception as e:
#             print(f'error connecting to websocket {e}')
#             raise Exception(f'error connecting to websocket {e}')
#         client_instance.artlet_client_store['websocket'] = ws
#         client_instance.artlet_client_store['server_address'] = server_address
#         return ws
    
#     def get_server_prompt_id(self, client_instance, api_file, server_address, prompt_block_id):
#         #first check if websocket is already open and established
        
#         if prompt_block_id not in ['', None] : 
#             # this block is dependent and shoud only return the prompt when it is already open
#             pbar = comfy.utils.ProgressBar(50)
#             progress = 0
#             #try for 5 times
#             for i in range(5):
#                 server_prompt_id = client_instance.artlet_client_store.get('server_prompt_id', None)
#                 if server_prompt_id:
#                     return server_prompt_id
#                 if progress % 20 == 0:
#                     print(f'sending throw_exception_if_processing_interrupted server_prompt_id {progress}')
#                 comfy.model_management.throw_exception_if_processing_interrupted()
#                 pbar.update_absolute(progress % 50, total=50)
#                 time.sleep(0.01)
#                 progress += 1
#             if server_prompt_id is None and api_file in [None, '']:  # neither api_file nor server_prompt_id is present
#                 raise Exception('error getting server prompt id')
            
#         # We are assuing every time 1st block is hit we need to start prompt a fresh
#         client_instance.artlet_client_store['server_prompt_id'] = None
#         if api_file is None or server_address is None:
#             print('this is first block so api_file and server_address should be present')
#             raise Exception('this is first block so api_file and server_address should be present')
#         server_prompt_id = None
#         with open(api_file, 'r') as f:
#             api = json.load(f)
        
#             p = {"prompt": api, "client_id": client_id, 'artlet_service':True}
#             data = json.dumps(p).encode('utf-8')
        
#             req =  urllib.request.Request("http://{}/prompt".format(server_address), data=data)
#             server_prompt_data = json.loads(urllib.request.urlopen(req).read())
#             server_prompt_id = server_prompt_data['prompt_id']
#             client_instance.artlet_client_store['server_prompt_id'] = server_prompt_id
#             return server_prompt_id
#       #  raise Exception('error getting server prompt id')

#     def send_server_inputs(self, server_address, server_prompt_id, server_block_id, input_0, input_1, input_2, 
#                            input_3, input_4, input_5, input_6, input_7, input_8, input_9): # for each input 
#         print('sending server inputs input0_to_9')
#         for i, input_data in enumerate([input_0,input_1,input_2,input_3,input_4,input_5,input_6,input_7,input_8,input_9]):
#             # if input_data is None:
#             #     continue
#             object_id = f'input_{i}'
#             bytecode_data = pickle.dumps(input_data)
#             url = f"http://{server_address}/artlet/object/{server_prompt_id}/{server_block_id}/input/{object_id}"
#             req = urllib.request.Request(
#                 url, 
#                 data=bytecode_data, 
#                 headers={"Content-Type": "application/octet-stream"}, 
#                 method="POST"
#             )
#             result = urllib.request.urlopen(req)
#         #now set the block input ready status   
#         url = f"http://{server_address}/artlet/object_status/{server_prompt_id}/{server_block_id}/input"
#         data=json.dumps({'status': 'ready'}).encode('utf-8')
#         req = urllib.request.Request(url, data=data, method="POST")
#         result = urllib.request.urlopen(req).read()
        
#     def get_output_status(self, server_address, server_prompt_id, server_block_id):
#         url = f"http://{server_address}/artlet/object_status/{server_prompt_id}/{server_block_id}/output"
#         req = urllib.request.Request(url, method="GET")
#         result = urllib.request.urlopen(req).read()
#         result = json.loads(result)
#         print('output status', result, 'for server_prompt_id', server_prompt_id, 'server_block_id', server_block_id)
#         return result.get('status', None)
    
#     def check_execution_complete(self, server_address, server_prompt_id, server_block_id):
#         return False # not implemented
#     def close_websocket(self, client_instance):
#         ws = client_instance.artlet_client_store.get('websocket', None)
#         if ws:
#             ws.close()
#             client_instance.artlet_client_store['websocket'] = None
#             client_instance.artlet_client_store['server_prompt_id'] = None
#         return
    
#     def get_outputs(self, server_address, server_prompt_id, server_block_id):
#         #first check if outputs are ready
#         pbar = comfy.utils.ProgressBar(1000)
#         progress = 0
#         while True:
#             output_status = self.get_output_status(server_address, server_prompt_id, server_block_id)
#             if output_status == 'ready':
#                 break
#             if progress % 1000 == 0:
#                 print(f'sending throw_exception_if_processing_interrupted get_outputs {progress}')
#             comfy.model_management.throw_exception_if_processing_interrupted()
#             pbar.update_absolute(progress % 1000, total=1000)
#             progress += 1
#             time.sleep(0.1)
#          #output is ready so get all the data
#         output_data = ()
#         for i in range(10):
#             object_id = f'output_{i}'
#             url = f"http://{server_address}/artlet/object/{server_prompt_id}/{server_block_id}/output/{object_id}"
#             with urllib.request.urlopen(url) as response:
#                 bytecode_data = response.read()
#                 print(f'got object data {len(bytecode_data)}')
#                 output_got = pickle.loads(bytecode_data)
                
#                 output_data = output_data + (output_got,)
#         print('output data', type(output_data))
#         return output_data
  

# class ArtletLoopDummy:
#     def __init__(self):
#         pass
#     @classmethod
#     def INPUT_TYPES(cls):
#         return {
#             "required": {
#             },
#         }
#     RETURN_TYPES = ()
#     RETURN_NAMES = ()
#     FUNCTION = "artlet_loop_dummy"
#     CATEGORY = "Artlet"
#     OUTPUT_NODE = True

#     def artlet_loop_dummy(self):
#         pbar = comfy.utils.ProgressBar(50)
#         progress = 0
#         while True:
#             if progress % 50 == 0:
#                 print(f'sending throw_exception_if_processing_interrupted artlet_loop_dummy {progress}')
#             comfy.model_management.throw_exception_if_processing_interrupted()
#             print('artlet loop dummy')
#             pbar.update_absolute(progress % 50, total=50)
#             time.sleep(0.01)
#             progress += 1
        
            


from qsCode.artlets.artlets_new import ArtletServerInputBlock, ArtletServerOutputBlock, ArtletClientBlock, ArtletDescription

NODE_CLASS_MAPPINGS = {
    "ArtletServerInputBlock": ArtletServerInputBlock,
    "ArtletServerOutputBlock": ArtletServerOutputBlock,
    "ArtletClientBlock": ArtletClientBlock,
    # "ArtletLoopDummy": ArtletLoopDummy,
    "ArtletDescription": ArtletDescription,
    
  }
NODE_DISPLAY_NAME_MAPPINGS = {
    "ArtletServerInputBlock": "Artlet Server Input Block",
    "ArtletServerOutputBlock": "Artlet Server Output Block",
    "ArtletClientBlock": "Artlet Client Block",
    # "ArtletLoopDummy": "Artlet Loop Dummy",
    "ArtletDescription": "Artlet Description",
  }

