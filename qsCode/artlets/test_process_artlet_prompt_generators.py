import json
import os
import sys
from artlet_helpers import process_artlet_prompt_generators

def create_test_prompt():
    """Create a test prompt with ArtletPromptGenerator nodes"""
    prompt = {
        "1": {
            "class_type": "CheckpointLoaderSimple",
            "inputs": {
                "ckpt_name": "sd_xl_base_1.0_0.9vae.safetensors"
            }
        },
        "2": {
            "class_type": "EmptyLatentImage",
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            }
        },
        "3": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": "beautiful landscape",
                "clip": ["1", 1]
            }
        },
        "4": {
            "class_type": "KSampler",
            "inputs": {
                "seed": 123456789,
                "steps": 20,
                "cfg": 8.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["1", 0],
                "positive": ["3", 0],
                "negative": ["5", 0],
                "latent_image": ["2", 0]
            }
        },
        "5": {
            "class_type": "CLIPTextEncode",
            "inputs": {
                "text": "text, watermark",
                "clip": ["1", 1]
            }
        },
        "6": {
            "class_type": "VAEDecode",
            "inputs": {
                "samples": ["4", 0],
                "vae": ["1", 2]
            }
        },
        "7": {
            "class_type": "PreviewImage",
            "inputs": {
                "images": ["6", 0]
            }
        },
        "8": {
            "class_type": "ArtletPromptGenerator",
            "inputs": {
                "server_node_list": "1,2,3",
                "sequence_id": 0,
                "run_nodes_on_server": True,
                "save_prompts": False,
                "client_block_id": "client_block_1",
                "server_block_id": "server_block_1",
                "server_address": "http://localhost:18994",
                "single_prompt_block": True,
                "server_api_file": "artlet/server/server_prompt_1.json",
                "client_api_file": "artlet/client/client_prompt_1.json",
                "update_int": 0
            }
        },
        "9": {
            "class_type": "ArtletPromptGenerator",
            "inputs": {
                "server_node_list": "5",
                "sequence_id": 1,
                "run_nodes_on_server": True,
                "save_prompts": True,
                "client_block_id": "client_block_2",
                "server_block_id": "server_block_2",
                "server_address": "http://localhost:18994",
                "single_prompt_block": True,
                "server_api_file": "artlet/server/server_prompt_2.json",
                "client_api_file": "artlet/client/client_prompt_2.json",
                "update_int": 0
            }
        }
    }
    return prompt

def test_process_artlet_prompt_generators():
    """Test the process_artlet_prompt_generators function"""
    # Create test prompt
    prompt = create_test_prompt()
    
    # Process the prompt
    run_nodes_on_server, save_prompts, client_prompt, client_prompt_file, server_prompts = process_artlet_prompt_generators(prompt,'dfdfdf')
    
    # Print results
    print(f"Run nodes on server: {run_nodes_on_server}")
    print(f"Save prompts: {save_prompts}")
    print(f"Client prompt file: {client_prompt_file}")
    print(f"Number of server prompts: {len(server_prompts)}")
    
    # Check if the client prompt has ArtletClientBlock nodes
    client_blocks = [node_id for node_id, node_data in client_prompt.items() 
                    if node_data.get("class_type") == "ArtletClientBlock"]
    print(f"Client blocks in client prompt: {client_blocks}")
    
    # Check if server nodes were removed from client prompt
    server_nodes_1 = prompt["8"]["inputs"]["server_node_list"].split(",")
    server_nodes_2 = prompt["9"]["inputs"]["server_node_list"].split(",")
    all_server_nodes = server_nodes_1 + server_nodes_2
    
    for node_id in all_server_nodes:
        if node_id in client_prompt:
            print(f"ERROR: Server node {node_id} still in client prompt")
    
    # Check server prompts
    for i, (server_prompt, server_file) in enumerate(server_prompts):
        print(f"\nServer prompt {i+1} file: {server_file}")
        print(f"Server prompt {i+1} nodes: {list(server_prompt.keys())}")
        
        # Check if server prompt has ArtletServerInputBlock and ArtletServerOutputBlock
        server_input_blocks = [node_id for node_id, node_data in server_prompt.items() 
                              if node_data.get("class_type") == "ArtletServerInputBlock"]
        server_output_blocks = [node_id for node_id, node_data in server_prompt.items() 
                               if node_data.get("class_type") == "ArtletServerOutputBlock"]
        
        print(f"Server input blocks: {server_input_blocks}")
        print(f"Server output blocks: {server_output_blocks}")
    
    return run_nodes_on_server, save_prompts, client_prompt, client_prompt_file, server_prompts

if __name__ == "__main__":
    # Add parent directory to path
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
    
    # Run test
    test_process_artlet_prompt_generators()
