# Use the official NVIDIA CUDA image as the base
FROM nvidia/cuda:12.4.0-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH
ENV PYTHON_VERSION=3.12.2
ENV CONDA_ENV_NAME=qs

# Set the working directory early
WORKDIR /qsComfy

# Copy the application code into the container.

# Install the required system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    wget \
    curl \
    git \
    ca-certificates \
    build-essential \
    libssl-dev \
    libffi-dev \
    openssh-server \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update && \
      apt-get -y install sudo

RUN apt-get update && apt-get install -y libgl1 libglib2.0-0

RUN apt-get install -y libsm6 libxext6 libxrender-dev


# Install Miniconda
RUN wget --quiet https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /tmp/miniconda.sh && \
    bash /tmp/miniconda.sh -b -p $CONDA_DIR && \
    rm /tmp/miniconda.sh

# Arguments for host UID and GID for user creation
ARG HOST_UID
ARG HOST_GID
RUN groupadd -g ${HOST_GID} qstools
# Create a non-root user 'qstools'
# Defaults to 1000 if HOST_UID/HOST_GID are not provided during build
RUN useradd -m -s /bin/bash -u ${HOST_UID} -g ${HOST_GID} qstools
# add qstools to sudo group
RUN usermod -aG sudo qstools

RUN echo "qstools ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

RUN mkdir -p /qsComfy
RUN chown -R qstools:qstools /qsComfy

# Copy the application code into the container.
# This ensures the copied files are owned by qstools.
COPY --chown=qstools:qstools ../.. /qsComfy/

# Set the SHELL to run subsequent commands within the conda environment
SHELL ["/bin/bash", "-c"]

ENV DEBIAN_FRONTEND=noninteractive
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH
ENV PYTHON_VERSION=3.12.2
ENV CONDA_ENV_NAME=qs

RUN  conda update -n base -c defaults conda -y && \
    conda init bash


# Create the Conda environment and install all packages in a single RUN layer
# This approach sources conda.sh, creates the env, activates it, and then installs packages.
RUN . $CONDA_DIR/etc/profile.d/conda.sh && \
    echo "Creating Conda environment '$CONDA_ENV_NAME' with Python $PYTHON_VERSION..." && \
    conda create -n $CONDA_ENV_NAME python=$PYTHON_VERSION -y && \
    echo "Activating Conda environment '$CONDA_ENV_NAME'..." && \
    conda activate $CONDA_ENV_NAME && \
    echo "Installing ffmpeg from conda-forge..." && \
    conda install -c conda-forge ffmpeg -y && \
    \
    echo "Installing PyTorch and related packages..." && \
    pip install torch==2.5.1+cu124 torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu124 && \
    \
    echo "Installing/Updating xformers..." && \
    pip install torch==2.5.1+cu124 -U  xformers --index-url https://download.pytorch.org/whl/cu124 && \
    \
    echo "Installing packages from /qsComfy/qsCode/qs_reqs/frozen_requirements.txt..." && \
    pip install --no-cache-dir -r /qsComfy/qsCode/qs_reqs/frozen_requirements.txt && \
    \
    # echo "Cleaning up Conda cache..." && \
    # conda clean -afy && \
    # Deactivate environment (good practice within the RUN layer)
    conda deactivate && \
    echo "All packages installed in '$CONDA_ENV_NAME' environment."

# Initialize Conda for the qstools user
RUN $CONDA_DIR/bin/conda init bash && \
    echo ". $CONDA_DIR/etc/profile.d/conda.sh" >> /home/<USER>/.bashrc && \
    echo "conda activate $CONDA_ENV_NAME" >> /home/<USER>/.bashrc && \
    chown qstools:qstools /home/<USER>/.bashrc
    
# Create empty directories for input, output, and models
# Ensure these are also owned by qstools.
RUN mkdir -p /qsComfy/input /qsComfy/output /qsComfy/models /qsComfy/artlet  /qsComfy/qs_custom_nodes && \
    mkdir -p /qsComfy/custom_nodes /qsComfy/user /qsComfy/my_workflows  /qsComfy/temp  /qsComfy/model_cache && \
    chown -R qstools:qstools /qsComfy/input /qsComfy/output /qsComfy/models /qsComfy/artlet /qsComfy/qs_custom_nodes && \
    chown -R qstools:qstools /qsComfy/custom_nodes /qsComfy/user /qsComfy/my_workflows /qsComfy/temp /qsComfy/model_cache

# Ensure entrypoint.sh is executable. This happens *after* the file is owned by qstools.
RUN cp /qsComfy/qsCode/docker/entrypoint.sh /qsComfy
RUN chmod +x /qsComfy/entrypoint.sh 
RUN chown qstools:qstools /qsComfy/entrypoint.sh

# Switch to the qstools user
USER qstools

# Set the working directory again for the new user (optional, but can be good for clarity)
WORKDIR /qsComfy

# Run the entrypoint.sh script by default
# We rely on the PATH being set correctly to find conda.
CMD ["conda", "run", "-n", "qs", "/qsComfy/entrypoint.sh"]

# Expose ports
EXPOSE 28890 
# 28891 28892