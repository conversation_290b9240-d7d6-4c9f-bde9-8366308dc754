#!/bin/bash
# This scrit is to test if check if newer version base_current_req.txt is working

# 1st parameter is given take it as requirements file

base_current_req="${1:-/qsfs2/services/qsComfy/qs_req/base_current_req.txt}"
test_env_name="${2:-qs_test}"
echo running with $base_current_req
source ~/.bashrc
#check if base_current_req file exists
if [ ! -f "$base_current_req" ]; then
    echo "$base_current_req file not found"
    exit 1
fi
#make sure test_env_name is not qsbase or qs
if [ $test_env_name == "qsbase" ] || [ $test_env_name == "qs" ]; then
    echo "test_env_name cannot be qsbase or qs"
    exit 1
fi
# check if test_env directory exists
if [ -d "/home/<USER>/.conda/envs/$test_env_name" ]; then
    echo "$test_env_name environment exist, do you want to delete and continue? (yes/no)"
    read answer
    if [ $answer == "yes" ]; then
        conda remove -n $test_env_name -y
    else
        exit 1
    fi
fi
#deactivate any current envs
 eval "$(conda shell.bash hook)" && conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null; conda deactivate 2>/tmp/null 

base_env_name="${test_env_name}_base"

conda create --name ${base_env_name} python=3.10.9 -y && eval "$(conda shell.bash hook)" && conda activate ${base_env_name}
if [ $? -ne 0 ]; then
    echo "${base_env_name} env could not be created"
    exit 1
fi
#install torch and xformers with pip method  this breaks sometime, but no choise
#install openimageio with conda method

conda install -y conda-forge::openimageio=*******
conda install -y -c conda-forge py-openimageio=*******

pip install torch==2.5.1+cu121 torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu121
pip install torch==2.5.1+cu121  -U xformers --index-url https://download.pytorch.org/whl/cu121
pip install torch==2.5.1+cu121 -r ${base_current_req} 

# check if torch is working 
python -c "import torch; print(torch.__version__)" 
if [ $? -eq 0 ]; then 
    echo "torch working with new requirements"
else
    echo "torch not working with new requirements, do you want to delete test envs? (yes/no)"
    read answer
    if [ $answer == "yes" ]; then
        conda  env remove -n ${base_env_name} -y        
        conda env remove -n $test_env_name -y
    else 
        exit 1
    fi
fi
echo "base envrionment ${base_env_name} env setup is complete"

conda deactivate

#now create conda env ${test_env_name} and update pythonpath,

conda create --name $test_env_name python=3.10.9 -y

export qs_env=/home/<USER>/.conda/envs/$test_env_name/
export qsbase_env=/home/<USER>/.conda/envs/$base_env_name/


mkdir -p /home/<USER>/.conda/envs/$test_env_name/etc/conda/activate.d
echo '#!/bin/bash' > /tmp/env_vars.sh && echo "export PYTHONPATH=\$PYTHONPATH:/home/<USER>/.conda/envs/$base_env_name/lib/python3.10/site-packages/" >> /tmp/env_vars.sh && echo "export PATH=\$PATH:/home/<USER>/.conda/envs/${base_env_name}/bin/" >> /tmp/env_vars.sh
cp /tmp/env_vars.sh /home/<USER>/.conda/envs/${test_env_name}/etc/conda/activate.d/
chmod +x /home/<USER>/.conda/envs/${test_env_name}/etc/conda/activate.d/env_vars.sh
conda activate ${test_env_name}
#check if torch is working
python -c "import torch; print(torch.__version__)"
if [ $? -eq 0 ]; then 
    echo "torch working with new requirements"
else
    echo "torch not working with new requirements, do you want to delete test envs? (yes/no)"
    read answer
    if [ $answer == "yes" ]; then
        conda  env remove -n ${base_env_name} -y        
        conda env remove -n $test_env_name -y
    else 
        exit 1
    fi
fi


echo "new env ${base_env_name} and ${test_env_name} created, run comfyui and check"

exit 0
